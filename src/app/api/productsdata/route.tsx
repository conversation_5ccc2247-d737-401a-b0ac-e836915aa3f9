import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";
import path from "path";
import {mkdir, writeFile} from "fs/promises";

type DataProps = {
    title: string | null,
    items: string | null,
    regular: string | null,
    discount: string | null,
    discrate: string | null,
    brandtitle: string | null,
    brandimg: string | null,
    parenttable: string | null,
    details: string | null
}

const prisma = new PrismaClient();

export async function POST(req:NextRequest){
    const formData = await req.formData();
    const obj = {} as DataProps;
    // get the file 
    const brandImg = formData.get("brandimg");
    const brandtitle = formData.get("brandtitle");
    // check whether if it's file then get the name
    let rename;

    if(brandImg instanceof File){
        const filename = brandImg.name;
        const splitname= filename.split(".");
        
        rename = splitname[1] + Date.now() + "." + splitname[1];
    }else{
        rename = brandImg
    }

    // config file to place in public folder
    const uploadDir = path.join(process.cwd(),"public/assets");
    const buffer = brandImg instanceof File ? Buffer.from(await brandImg.arrayBuffer()) : "no file received";

    // retrieve data to obj
    formData.entries().forEach(([key,value])=>{
        obj[key as keyof DataProps] = value.toString() as string;
    });

    // replace obj->brandimg value if file existed
    if(brandImg instanceof File){
        obj.brandimg = `/assets/${rename}`
    }

    try{
        if(brandImg instanceof File){
            await mkdir(uploadDir,{recursive:true});

            await writeFile(path.join(process.cwd(),"public/assets/" + rename),buffer);

            await prisma.sponserimg.create({
                data:{
                    imgpath: rename,
                    title: brandtitle?.toString()
                }
            })
        }

        const postData = await prisma.products.create({
            data:obj
        });

        const parenttable = postData.parenttable;

        return NextResponse.json({message:'product added',code:parenttable},{status:200});
    }catch(error){
        return NextResponse.json({message:error});
    }
}