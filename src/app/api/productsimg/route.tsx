import { mkdir, writeFile } from "fs/promises";
import { NextRequest, NextResponse } from "next/server";
import path from "path";
import { PrismaClient } from "../../../../generated/prisma";

const prisma = new PrismaClient();

export async function POST(req:NextRequest){
    const formData = await req.formData();
    const parenttable = formData.get("parenttable");
    const arr = [...formData.entries()];

    const reformat =await Promise.all(arr.map(async([key,value])=>{
        if(value instanceof File){
            const [name,ext] = value.name.split(".");
            const rename = `${name}${Date.now()}.${ext}`;

            // cofigue file for the placement to the public folder
            const uploadDir = path.join(process.cwd(),"public/assets");
            const buffer = Buffer.from(await value.arrayBuffer());

            // place it to the public folder
            await mkdir(uploadDir,{recursive:true});
            await writeFile(path.join(process.cwd(),"public/assets/" + rename),buffer);

            // return the the object that will insert to the database
            return {title:rename,imgpath:`public/assets/${rename}`,parenttable:typeof parenttable === "string" ? parenttable : null}
        }

        return null
    }));

    const filteredArr = reformat.filter((items)=>items != null)

    try{
        await prisma.productimgs.createMany({
            data: filteredArr.map((items)=>({
                title: items.title.toString(),
                imgpath: items.imgpath.toString(),
                parenttable: items?.parenttable?.toString()
            }))
        })

        return NextResponse.json({message:"image added"},{status:200})
    }catch(error){
        return NextResponse.json(error)
    }
}